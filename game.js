// 3D飞机模拟游戏
class FlightSimulator {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.airplane = null;
        this.clouds = [];
        this.terrain = null;
        
        // 飞机状态
        this.airplaneState = {
            position: { x: 0, y: 100, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            velocity: { x: 0, y: 0, z: 0 },
            speed: 0,
            altitude: 100,
            heading: 0
        };
        
        // 控制状态
        this.controls = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            up: false,
            down: false,
            boost: false,
            turnLeft: false,
            turnRight: false
        };
        
        // 鼠标控制
        this.mouse = {
            x: 0,
            y: 0,
            sensitivity: 0.002
        };
        
        this.init();
    }
    
    init() {
        this.createScene();
        this.createAirplane();
        this.createTerrain();
        this.createClouds();
        this.createLighting();
        this.setupControls();
        this.animate();
        
        // 添加十字准星
        this.addCrosshair();
        
        // 更新游戏状态
        document.getElementById('gameStatus').textContent = '游戏运行中';
    }
    
    createScene() {
        // 创建场景
        this.scene = new THREE.Scene();

        // 创建天空盒
        this.createSkybox();

        // 设置雾效
        this.scene.fog = new THREE.Fog(0x87CEEB, 2000, 15000);

        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            20000
        );

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;

        document.getElementById('gameContainer').appendChild(this.renderer.domElement);

        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }

    createSkybox() {
        // 创建天空盒几何体
        const skyGeometry = new THREE.SphereGeometry(15000, 32, 32);

        // 创建天空渐变材质
        const skyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                topColor: { value: new THREE.Color(0x0077ff) },
                bottomColor: { value: new THREE.Color(0xffffff) },
                offset: { value: 400 },
                exponent: { value: 0.6 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                void main() {
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 topColor;
                uniform vec3 bottomColor;
                uniform float offset;
                uniform float exponent;
                varying vec3 vWorldPosition;
                void main() {
                    float h = normalize(vWorldPosition + offset).y;
                    gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
                }
            `,
            side: THREE.BackSide
        });

        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }
    
    createAirplane() {
        // 创建现代战斗机模型
        const airplaneGroup = new THREE.Group();

        // 高质量材质
        const fuselageMaterial = new THREE.MeshPhongMaterial({
            color: 0x4a5568,
            shininess: 100,
            specular: 0x666666,
            reflectivity: 0.3
        });

        const wingMaterial = new THREE.MeshPhongMaterial({
            color: 0x2d3748,
            shininess: 80,
            specular: 0x555555
        });

        const canopyMaterial = new THREE.MeshPhongMaterial({
            color: 0x1a202c,
            shininess: 200,
            specular: 0x888888,
            transparent: true,
            opacity: 0.8
        });

        const accentMaterial = new THREE.MeshPhongMaterial({
            color: 0xff6b35,
            shininess: 120,
            specular: 0x333333,
            emissive: 0x331100
        });

        // 主机身 - 流线型战斗机造型
        const fuselageGeometry = new THREE.CylinderGeometry(0.4, 1.2, 12, 16);
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        fuselage.scale.set(1, 1, 0.8);
        fuselage.castShadow = true;
        fuselage.receiveShadow = true;
        airplaneGroup.add(fuselage);

        // 机头锥形
        const noseGeometry = new THREE.ConeGeometry(0.4, 3, 12);
        const nose = new THREE.Mesh(noseGeometry, fuselageMaterial);
        nose.rotation.z = -Math.PI / 2;
        nose.position.x = 7.5;
        nose.castShadow = true;
        airplaneGroup.add(nose);

        // 驾驶舱
        const canopyGeometry = new THREE.SphereGeometry(0.8, 12, 8);
        const canopy = new THREE.Mesh(canopyGeometry, canopyMaterial);
        canopy.position.set(2, 0, 0.3);
        canopy.scale.set(1.5, 1, 0.8);
        canopy.castShadow = true;
        airplaneGroup.add(canopy);

        // 主翼 - 三角翼设计
        const wingShape = new THREE.Shape();
        wingShape.moveTo(0, 0);
        wingShape.lineTo(-6, 8);
        wingShape.lineTo(-2, 10);
        wingShape.lineTo(2, 2);
        wingShape.lineTo(0, 0);

        const wingGeometry = new THREE.ExtrudeGeometry(wingShape, {
            depth: 0.4,
            bevelEnabled: true,
            bevelThickness: 0.1,
            bevelSize: 0.1
        });

        const leftWing = new THREE.Mesh(wingGeometry, wingMaterial);
        leftWing.position.set(0, 0, -0.5);
        leftWing.castShadow = true;
        airplaneGroup.add(leftWing);

        const rightWing = new THREE.Mesh(wingGeometry, wingMaterial);
        rightWing.position.set(0, 0, -0.5);
        rightWing.rotation.z = Math.PI;
        rightWing.castShadow = true;
        airplaneGroup.add(rightWing);

        // 垂直尾翼
        const verticalTailGeometry = new THREE.BoxGeometry(0.3, 5, 3);
        const verticalTail = new THREE.Mesh(verticalTailGeometry, wingMaterial);
        verticalTail.position.x = -5;
        verticalTail.position.z = 1.5;
        verticalTail.castShadow = true;
        airplaneGroup.add(verticalTail);

        // 水平尾翼
        const horizontalTailGeometry = new THREE.BoxGeometry(3, 0.2, 2);
        const horizontalTail = new THREE.Mesh(horizontalTailGeometry, wingMaterial);
        horizontalTail.position.x = -5;
        horizontalTail.position.z = 0.5;
        horizontalTail.castShadow = true;
        airplaneGroup.add(horizontalTail);

        // 喷气发动机
        const engineGeometry = new THREE.CylinderGeometry(0.6, 0.8, 3, 12);
        const engineMaterial = new THREE.MeshPhongMaterial({
            color: 0x1a1a1a,
            shininess: 150,
            specular: 0x444444
        });

        const engine = new THREE.Mesh(engineGeometry, engineMaterial);
        engine.rotation.z = Math.PI / 2;
        engine.position.set(-4, 0, -0.5);
        engine.castShadow = true;
        airplaneGroup.add(engine);

        // 发动机喷口
        const nozzleGeometry = new THREE.CylinderGeometry(0.5, 0.7, 1, 8);
        const nozzleMaterial = new THREE.MeshPhongMaterial({
            color: 0x333333,
            emissive: 0x110000
        });

        const nozzle = new THREE.Mesh(nozzleGeometry, nozzleMaterial);
        nozzle.rotation.z = Math.PI / 2;
        nozzle.position.set(-6.5, 0, -0.5);
        nozzle.castShadow = true;
        airplaneGroup.add(nozzle);

        // 导弹挂架
        const pylonGeometry = new THREE.BoxGeometry(0.2, 1, 0.3);
        const pylonMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });

        const leftPylon = new THREE.Mesh(pylonGeometry, pylonMaterial);
        leftPylon.position.set(-1, 4, -1);
        airplaneGroup.add(leftPylon);

        const rightPylon = new THREE.Mesh(pylonGeometry, pylonMaterial);
        rightPylon.position.set(-1, -4, -1);
        airplaneGroup.add(rightPylon);

        // 导弹
        const missileGeometry = new THREE.CylinderGeometry(0.1, 0.1, 2, 8);
        const missileMaterial = new THREE.MeshPhongMaterial({
            color: 0x666666,
            shininess: 100
        });

        const leftMissile = new THREE.Mesh(missileGeometry, missileMaterial);
        leftMissile.rotation.z = Math.PI / 2;
        leftMissile.position.set(-1, 4, -1.5);
        airplaneGroup.add(leftMissile);

        const rightMissile = new THREE.Mesh(missileGeometry, missileMaterial);
        rightMissile.rotation.z = Math.PI / 2;
        rightMissile.position.set(-1, -4, -1.5);
        airplaneGroup.add(rightMissile);

        // 机身细节条纹
        const stripeGeometry = new THREE.BoxGeometry(8, 0.1, 0.1);
        const stripeMaterial = new THREE.MeshPhongMaterial({
            color: 0xff6b35,
            emissive: 0x331100
        });

        const stripe1 = new THREE.Mesh(stripeGeometry, stripeMaterial);
        stripe1.position.set(0, 0, 0.8);
        airplaneGroup.add(stripe1);

        const stripe2 = new THREE.Mesh(stripeGeometry, stripeMaterial);
        stripe2.position.set(0, 0, -0.8);
        airplaneGroup.add(stripe2);

        // 创建发动机尾焰粒子系统
        this.createEngineFlame(airplaneGroup);

        this.airplane = airplaneGroup;
        this.airplane.position.set(0, 1000, 0);
        this.scene.add(this.airplane);

        // 设置相机跟随飞机
        this.camera.position.set(0, 1005, -30);
        this.camera.lookAt(this.airplane.position);
    }

    createEngineFlame(airplaneGroup) {
        // 创建发动机尾焰粒子系统
        const particleCount = 50;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            positions[i3] = -7 - Math.random() * 3; // x位置（发动机后方）
            positions[i3 + 1] = (Math.random() - 0.5) * 0.5; // y位置
            positions[i3 + 2] = -0.5 + (Math.random() - 0.5) * 0.5; // z位置

            // 火焰颜色（橙红色到黄色）
            const intensity = Math.random();
            colors[i3] = 1.0; // 红色
            colors[i3 + 1] = 0.3 + intensity * 0.7; // 绿色
            colors[i3 + 2] = 0.0; // 蓝色

            sizes[i] = Math.random() * 0.3 + 0.1;
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const flameMaterial = new THREE.PointsMaterial({
            size: 0.2,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });

        this.engineFlame = new THREE.Points(particles, flameMaterial);
        airplaneGroup.add(this.engineFlame);
    }
    
    createTerrain() {
        // 创建更精美的地形
        const groundGeometry = new THREE.PlaneGeometry(30000, 30000, 200, 200);

        // 创建地形纹理材质
        const groundMaterial = new THREE.MeshPhongMaterial({
            color: 0x3a5f3a,
            shininess: 10,
            specular: 0x111111
        });

        // 添加更复杂的地形高度变化
        const vertices = groundGeometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const x = vertices[i];
            const y = vertices[i + 1];

            // 使用多层噪声创建更自然的地形
            let height = 0;
            height += Math.sin(x * 0.0001) * 50;
            height += Math.sin(y * 0.0001) * 50;
            height += Math.sin(x * 0.001) * 20;
            height += Math.sin(y * 0.001) * 20;
            height += Math.random() * 10 - 5;

            vertices[i + 2] = height;
        }
        groundGeometry.attributes.position.needsUpdate = true;
        groundGeometry.computeVertexNormals();

        this.terrain = new THREE.Mesh(groundGeometry, groundMaterial);
        this.terrain.rotation.x = -Math.PI / 2;
        this.terrain.position.y = 0;
        this.terrain.receiveShadow = true;
        this.scene.add(this.terrain);

        // 添加水面
        const waterGeometry = new THREE.PlaneGeometry(5000, 5000);
        const waterMaterial = new THREE.MeshPhongMaterial({
            color: 0x006994,
            transparent: true,
            opacity: 0.8,
            shininess: 100,
            specular: 0x555555
        });

        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.rotation.x = -Math.PI / 2;
        water.position.set(8000, 20, 8000);
        water.receiveShadow = true;
        this.scene.add(water);
    }
    
    createClouds() {
        // 创建更真实的云朵
        for (let i = 0; i < 80; i++) {
            const cloudGroup = new THREE.Group();

            // 创建多个球体组成一朵云
            const numSpheres = Math.floor(Math.random() * 8) + 5;
            for (let j = 0; j < numSpheres; j++) {
                const cloudGeometry = new THREE.SphereGeometry(
                    Math.random() * 30 + 15,
                    12,
                    8
                );

                const cloudMaterial = new THREE.MeshPhongMaterial({
                    color: 0xffffff,
                    transparent: true,
                    opacity: Math.random() * 0.4 + 0.6,
                    shininess: 10
                });

                const cloudPart = new THREE.Mesh(cloudGeometry, cloudMaterial);
                cloudPart.position.set(
                    (Math.random() - 0.5) * 60,
                    (Math.random() - 0.5) * 20,
                    (Math.random() - 0.5) * 60
                );
                cloudPart.castShadow = false;
                cloudPart.receiveShadow = false;

                cloudGroup.add(cloudPart);
            }

            cloudGroup.position.set(
                (Math.random() - 0.5) * 15000,
                Math.random() * 1000 + 500,
                (Math.random() - 0.5) * 15000
            );

            // 添加缓慢的云朵移动
            cloudGroup.userData = {
                velocity: {
                    x: (Math.random() - 0.5) * 0.1,
                    z: (Math.random() - 0.5) * 0.1
                }
            };

            this.clouds.push(cloudGroup);
            this.scene.add(cloudGroup);
        }
    }
    
    createLighting() {
        // 环境光 - 模拟天空散射光
        const ambientLight = new THREE.AmbientLight(0x87CEEB, 0.4);
        this.scene.add(ambientLight);

        // 主太阳光
        const sunLight = new THREE.DirectionalLight(0xffffff, 1.2);
        sunLight.position.set(1000, 2000, 500);
        sunLight.castShadow = true;
        sunLight.shadow.mapSize.width = 4096;
        sunLight.shadow.mapSize.height = 4096;
        sunLight.shadow.camera.near = 0.5;
        sunLight.shadow.camera.far = 5000;
        sunLight.shadow.camera.left = -2000;
        sunLight.shadow.camera.right = 2000;
        sunLight.shadow.camera.top = 2000;
        sunLight.shadow.camera.bottom = -2000;
        sunLight.shadow.bias = -0.0001;
        this.scene.add(sunLight);

        // 添加半球光模拟天空和地面的反射光
        const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x3a5f3a, 0.6);
        this.scene.add(hemisphereLight);

        // 添加点光源模拟飞机导航灯
        const navLightLeft = new THREE.PointLight(0xff0000, 1, 100);
        navLightLeft.position.set(0, 8, -1);
        this.airplane.add(navLightLeft);

        const navLightRight = new THREE.PointLight(0x00ff00, 1, 100);
        navLightRight.position.set(0, -8, -1);
        this.airplane.add(navLightRight);

        const tailLight = new THREE.PointLight(0xffffff, 0.8, 60);
        tailLight.position.set(-5, 0, 1.5);
        this.airplane.add(tailLight);

        // 添加频闪效果
        this.navLights = [navLightLeft, navLightRight, tailLight];
        this.strobeTimer = 0;

        // 存储太阳光引用用于动态调整
        this.sunLight = sunLight;
    }

    setupControls() {
        // 键盘控制
        document.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyW':
                    this.controls.up = true;
                    break;
                case 'KeyS':
                    this.controls.down = true;
                    break;
                case 'KeyA':
                    this.controls.left = true;
                    break;
                case 'KeyD':
                    this.controls.right = true;
                    break;
                case 'KeyQ':
                    this.controls.turnLeft = true;
                    break;
                case 'KeyE':
                    this.controls.turnRight = true;
                    break;
                case 'Space':
                    this.controls.boost = true;
                    event.preventDefault();
                    break;
            }
        });

        document.addEventListener('keyup', (event) => {
            switch(event.code) {
                case 'KeyW':
                    this.controls.up = false;
                    break;
                case 'KeyS':
                    this.controls.down = false;
                    break;
                case 'KeyA':
                    this.controls.left = false;
                    break;
                case 'KeyD':
                    this.controls.right = false;
                    break;
                case 'KeyQ':
                    this.controls.turnLeft = false;
                    break;
                case 'KeyE':
                    this.controls.turnRight = false;
                    break;
                case 'Space':
                    this.controls.boost = false;
                    break;
            }
        });

        // 鼠标控制
        document.addEventListener('mousemove', (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        });

        // 锁定鼠标指针
        document.addEventListener('click', () => {
            document.body.requestPointerLock();
        });
    }

    updatePhysics() {
        const deltaTime = 0.016; // 假设60FPS

        // 基础推力
        let thrust = 50;
        if (this.controls.boost) {
            thrust = 100;
        }

        // 控制输入
        if (this.controls.up) {
            this.airplaneState.rotation.x -= 0.02;
        }
        if (this.controls.down) {
            this.airplaneState.rotation.x += 0.02;
        }
        if (this.controls.left) {
            this.airplaneState.rotation.z += 0.03;
        }
        if (this.controls.right) {
            this.airplaneState.rotation.z -= 0.03;
        }
        if (this.controls.turnLeft) {
            this.airplaneState.rotation.y += 0.02;
        }
        if (this.controls.turnRight) {
            this.airplaneState.rotation.y -= 0.02;
        }

        // 鼠标控制
        this.airplaneState.rotation.y += this.mouse.x * this.mouse.sensitivity;
        this.airplaneState.rotation.x += this.mouse.y * this.mouse.sensitivity;

        // 限制旋转角度
        this.airplaneState.rotation.x = Math.max(-Math.PI/3, Math.min(Math.PI/3, this.airplaneState.rotation.x));
        this.airplaneState.rotation.z = Math.max(-Math.PI/4, Math.min(Math.PI/4, this.airplaneState.rotation.z));

        // 计算前进方向
        const forward = new THREE.Vector3(
            Math.sin(this.airplaneState.rotation.y),
            Math.sin(this.airplaneState.rotation.x),
            Math.cos(this.airplaneState.rotation.y)
        );

        // 应用推力
        this.airplaneState.velocity.x += forward.x * thrust * deltaTime;
        this.airplaneState.velocity.y += forward.y * thrust * deltaTime;
        this.airplaneState.velocity.z += forward.z * thrust * deltaTime;

        // 重力
        this.airplaneState.velocity.y -= 20 * deltaTime;

        // 空气阻力
        const drag = 0.98;
        this.airplaneState.velocity.x *= drag;
        this.airplaneState.velocity.y *= drag;
        this.airplaneState.velocity.z *= drag;

        // 更新位置
        this.airplaneState.position.x += this.airplaneState.velocity.x * deltaTime;
        this.airplaneState.position.y += this.airplaneState.velocity.y * deltaTime;
        this.airplaneState.position.z += this.airplaneState.velocity.z * deltaTime;

        // 防止飞机撞地
        if (this.airplaneState.position.y < 5) {
            this.airplaneState.position.y = 5;
            this.airplaneState.velocity.y = Math.max(0, this.airplaneState.velocity.y);
        }

        // 计算速度和高度
        this.airplaneState.speed = Math.sqrt(
            this.airplaneState.velocity.x ** 2 +
            this.airplaneState.velocity.z ** 2
        ) * 3.6; // 转换为km/h

        this.airplaneState.altitude = this.airplaneState.position.y;
        this.airplaneState.heading = (this.airplaneState.rotation.y * 180 / Math.PI + 360) % 360;
    }

    updateAirplane() {
        // 更新飞机位置和旋转
        this.airplane.position.set(
            this.airplaneState.position.x,
            this.airplaneState.position.y,
            this.airplaneState.position.z
        );

        this.airplane.rotation.set(
            this.airplaneState.rotation.x,
            this.airplaneState.rotation.y,
            this.airplaneState.rotation.z
        );
    }

    updateCamera() {
        // 第三人称视角跟随飞机
        const offset = new THREE.Vector3(0, 5, -20);
        offset.applyQuaternion(this.airplane.quaternion);

        this.camera.position.copy(this.airplane.position).add(offset);
        this.camera.lookAt(this.airplane.position);
    }

    updateUI() {
        // 更新仪表盘 - 转换为航空单位
        const speedKnots = this.airplaneState.speed * 0.539957; // km/h 转换为节
        const altitudeFeet = this.airplaneState.altitude * 3.28084; // 米转换为英尺

        document.getElementById('speedValue').textContent = Math.round(speedKnots);
        document.getElementById('altitudeValue').textContent = Math.round(altitudeFeet);
        document.getElementById('headingValue').textContent = Math.round(this.airplaneState.heading);

        // 更新人工地平仪
        this.updateAttitudeIndicator();

        // 更新速度矢量指示器
        this.updateVelocityVector();
    }

    updateAttitudeIndicator() {
        const horizon = document.getElementById('attitudeHorizon');
        const attitudeDisplay = document.querySelector('.attitude-display');

        if (horizon && attitudeDisplay) {
            // 根据飞机的俯仰角和横滚角更新地平线
            const pitch = this.airplaneState.rotation.x * (180 / Math.PI);
            const roll = this.airplaneState.rotation.z * (180 / Math.PI);

            // 更新地平线位置（俯仰）
            const pitchOffset = pitch * 2; // 放大俯仰效果
            horizon.style.transform = `translateY(${-pitchOffset}px)`;

            // 更新整个姿态显示的旋转（横滚）
            attitudeDisplay.style.transform = `translate(-50%, -50%) rotate(${roll}deg)`;
        }
    }

    updateVelocityVector() {
        const velocityVector = document.getElementById('velocityVector');
        if (velocityVector) {
            // 计算速度矢量在屏幕上的位置
            const velocity = new THREE.Vector3(
                this.airplaneState.velocity.x,
                this.airplaneState.velocity.y,
                this.airplaneState.velocity.z
            );

            // 将速度矢量投影到屏幕坐标
            const screenPosition = velocity.clone().project(this.camera);

            const x = (screenPosition.x * 0.5 + 0.5) * window.innerWidth;
            const y = (screenPosition.y * -0.5 + 0.5) * window.innerHeight;

            velocityVector.style.left = `${x}px`;
            velocityVector.style.top = `${y}px`;

            // 根据速度大小调整透明度
            const speed = velocity.length();
            const opacity = Math.min(speed / 50, 1);
            velocityVector.style.opacity = opacity;
        }
    }

    addCrosshair() {
        // 添加十字准星到HTML
        const crosshair = document.createElement('div');
        crosshair.className = 'crosshair';
        document.body.appendChild(crosshair);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        // 更新物理
        this.updatePhysics();

        // 更新飞机
        this.updateAirplane();

        // 添加发动机尾焰效果
        this.updateEngineEffects();

        // 更新导航灯频闪
        this.updateNavigationLights();

        // 更新云朵移动
        this.updateClouds();

        // 更新相机
        this.updateCamera();

        // 更新UI
        this.updateUI();

        // 动态调整太阳光
        this.updateLighting();

        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }

    updateClouds() {
        // 缓慢移动云朵
        this.clouds.forEach(cloud => {
            if (cloud.userData && cloud.userData.velocity) {
                cloud.position.x += cloud.userData.velocity.x;
                cloud.position.z += cloud.userData.velocity.z;

                // 如果云朵移动太远，重新定位
                const distance = Math.sqrt(
                    cloud.position.x ** 2 + cloud.position.z ** 2
                );
                if (distance > 20000) {
                    cloud.position.set(
                        (Math.random() - 0.5) * 15000,
                        cloud.position.y,
                        (Math.random() - 0.5) * 15000
                    );
                }
            }
        });
    }

    updateEngineEffects() {
        if (this.engineFlame) {
            const positions = this.engineFlame.geometry.attributes.position.array;
            const colors = this.engineFlame.geometry.attributes.color.array;
            const speed = this.airplaneState.speed;

            // 根据速度调整尾焰强度
            const flameIntensity = Math.min(speed / 100, 1);

            for (let i = 0; i < positions.length; i += 3) {
                // 更新粒子位置
                positions[i] -= 0.2; // 向后移动

                // 如果粒子移动太远，重新生成
                if (positions[i] < -10) {
                    positions[i] = -7;
                    positions[i + 1] = (Math.random() - 0.5) * 0.5;
                    positions[i + 2] = -0.5 + (Math.random() - 0.5) * 0.5;
                }

                // 更新颜色强度
                const colorIndex = Math.floor(i / 3) * 3;
                colors[colorIndex + 1] = (0.3 + Math.random() * 0.7) * flameIntensity;
            }

            this.engineFlame.geometry.attributes.position.needsUpdate = true;
            this.engineFlame.geometry.attributes.color.needsUpdate = true;
            this.engineFlame.material.opacity = 0.8 * flameIntensity;
        }
    }

    updateNavigationLights() {
        // 导航灯频闪效果
        this.strobeTimer += 0.016; // 假设60FPS

        if (this.navLights) {
            const strobePattern = Math.sin(this.strobeTimer * 8) > 0.5;
            const dimPattern = Math.sin(this.strobeTimer * 2) * 0.5 + 0.5;

            // 红绿导航灯频闪
            this.navLights[0].intensity = strobePattern ? 1.5 : 0.3; // 左红灯
            this.navLights[1].intensity = strobePattern ? 1.5 : 0.3; // 右绿灯

            // 尾灯缓慢闪烁
            this.navLights[2].intensity = 0.5 + dimPattern * 0.5;
        }
    }

    updateLighting() {
        // 根据高度调整光照强度
        const altitude = this.airplaneState.altitude;
        const intensity = Math.min(1.2, 0.8 + altitude * 0.0002);
        this.sunLight.intensity = intensity;

        // 根据时间或高度调整雾的颜色和密度
        if (altitude > 2000) {
            this.scene.fog.color.setHex(0xb8d4f0); // 高空更蓝
            this.scene.fog.near = 3000;
            this.scene.fog.far = 20000;
        } else {
            this.scene.fog.color.setHex(0x87CEEB); // 低空正常
            this.scene.fog.near = 2000;
            this.scene.fog.far = 15000;
        }
    }
}

// 当页面加载完成后启动游戏
window.addEventListener('load', () => {
    new FlightSimulator();
});
