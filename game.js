// 3D飞机模拟游戏
class FlightSimulator {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.airplane = null;
        this.clouds = [];
        this.terrain = null;
        
        // 飞机状态
        this.airplaneState = {
            position: { x: 0, y: 100, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            velocity: { x: 0, y: 0, z: 0 },
            speed: 0,
            altitude: 100,
            heading: 0
        };
        
        // 控制状态
        this.controls = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            up: false,
            down: false,
            boost: false,
            turnLeft: false,
            turnRight: false
        };
        
        // 鼠标控制
        this.mouse = {
            x: 0,
            y: 0,
            sensitivity: 0.002
        };
        
        this.init();
    }
    
    init() {
        this.createScene();
        this.createAirplane();
        this.createTerrain();
        this.createClouds();
        this.createLighting();
        this.setupControls();
        this.animate();
        
        // 添加十字准星
        this.addCrosshair();
        
        // 更新游戏状态
        document.getElementById('gameStatus').textContent = '游戏运行中';
    }
    
    createScene() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 1000, 10000);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            10000
        );
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        document.getElementById('gameContainer').appendChild(this.renderer.domElement);
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }
    
    createAirplane() {
        // 创建简单的飞机模型
        const airplaneGroup = new THREE.Group();
        
        // 机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.5, 1, 8, 8);
        const fuselageMaterial = new THREE.MeshLambertMaterial({ color: 0x0066cc });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        fuselage.castShadow = true;
        airplaneGroup.add(fuselage);
        
        // 主翼
        const wingGeometry = new THREE.BoxGeometry(12, 0.2, 2);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x004499 });
        const wing = new THREE.Mesh(wingGeometry, wingMaterial);
        wing.position.z = -1;
        wing.castShadow = true;
        airplaneGroup.add(wing);
        
        // 尾翼
        const tailGeometry = new THREE.BoxGeometry(3, 2, 0.2);
        const tailMaterial = new THREE.MeshLambertMaterial({ color: 0x004499 });
        const tail = new THREE.Mesh(tailGeometry, tailMaterial);
        tail.position.x = -3.5;
        tail.position.y = 1;
        tail.castShadow = true;
        airplaneGroup.add(tail);
        
        // 垂直尾翼
        const verticalTailGeometry = new THREE.BoxGeometry(0.2, 3, 2);
        const verticalTail = new THREE.Mesh(verticalTailGeometry, tailMaterial);
        verticalTail.position.x = -3.5;
        verticalTail.position.y = 1.5;
        verticalTail.castShadow = true;
        airplaneGroup.add(verticalTail);
        
        this.airplane = airplaneGroup;
        this.airplane.position.set(0, 100, 0);
        this.scene.add(this.airplane);
        
        // 设置相机跟随飞机
        this.camera.position.set(0, 105, -20);
        this.camera.lookAt(this.airplane.position);
    }
    
    createTerrain() {
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(20000, 20000, 100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x228B22,
            wireframe: false
        });
        
        // 添加地形高度变化
        const vertices = groundGeometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            vertices[i + 2] = Math.random() * 20 - 10; // z坐标随机高度
        }
        groundGeometry.attributes.position.needsUpdate = true;
        groundGeometry.computeVertexNormals();
        
        this.terrain = new THREE.Mesh(groundGeometry, groundMaterial);
        this.terrain.rotation.x = -Math.PI / 2;
        this.terrain.position.y = -50;
        this.terrain.receiveShadow = true;
        this.scene.add(this.terrain);
    }
    
    createClouds() {
        // 创建云朵
        for (let i = 0; i < 50; i++) {
            const cloudGeometry = new THREE.SphereGeometry(
                Math.random() * 20 + 10,
                8,
                6
            );
            const cloudMaterial = new THREE.MeshLambertMaterial({ 
                color: 0xffffff,
                transparent: true,
                opacity: 0.8
            });
            
            const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);
            cloud.position.set(
                (Math.random() - 0.5) * 5000,
                Math.random() * 200 + 50,
                (Math.random() - 0.5) * 5000
            );
            
            this.clouds.push(cloud);
            this.scene.add(cloud);
        }
    }
    
    createLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // 方向光（太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(100, 200, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 1000;
        directionalLight.shadow.camera.left = -500;
        directionalLight.shadow.camera.right = 500;
        directionalLight.shadow.camera.top = 500;
        directionalLight.shadow.camera.bottom = -500;
        this.scene.add(directionalLight);
    }

    setupControls() {
        // 键盘控制
        document.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyW':
                    this.controls.up = true;
                    break;
                case 'KeyS':
                    this.controls.down = true;
                    break;
                case 'KeyA':
                    this.controls.left = true;
                    break;
                case 'KeyD':
                    this.controls.right = true;
                    break;
                case 'KeyQ':
                    this.controls.turnLeft = true;
                    break;
                case 'KeyE':
                    this.controls.turnRight = true;
                    break;
                case 'Space':
                    this.controls.boost = true;
                    event.preventDefault();
                    break;
            }
        });

        document.addEventListener('keyup', (event) => {
            switch(event.code) {
                case 'KeyW':
                    this.controls.up = false;
                    break;
                case 'KeyS':
                    this.controls.down = false;
                    break;
                case 'KeyA':
                    this.controls.left = false;
                    break;
                case 'KeyD':
                    this.controls.right = false;
                    break;
                case 'KeyQ':
                    this.controls.turnLeft = false;
                    break;
                case 'KeyE':
                    this.controls.turnRight = false;
                    break;
                case 'Space':
                    this.controls.boost = false;
                    break;
            }
        });

        // 鼠标控制
        document.addEventListener('mousemove', (event) => {
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        });

        // 锁定鼠标指针
        document.addEventListener('click', () => {
            document.body.requestPointerLock();
        });
    }

    updatePhysics() {
        const deltaTime = 0.016; // 假设60FPS

        // 基础推力
        let thrust = 50;
        if (this.controls.boost) {
            thrust = 100;
        }

        // 控制输入
        if (this.controls.up) {
            this.airplaneState.rotation.x -= 0.02;
        }
        if (this.controls.down) {
            this.airplaneState.rotation.x += 0.02;
        }
        if (this.controls.left) {
            this.airplaneState.rotation.z += 0.03;
        }
        if (this.controls.right) {
            this.airplaneState.rotation.z -= 0.03;
        }
        if (this.controls.turnLeft) {
            this.airplaneState.rotation.y += 0.02;
        }
        if (this.controls.turnRight) {
            this.airplaneState.rotation.y -= 0.02;
        }

        // 鼠标控制
        this.airplaneState.rotation.y += this.mouse.x * this.mouse.sensitivity;
        this.airplaneState.rotation.x += this.mouse.y * this.mouse.sensitivity;

        // 限制旋转角度
        this.airplaneState.rotation.x = Math.max(-Math.PI/3, Math.min(Math.PI/3, this.airplaneState.rotation.x));
        this.airplaneState.rotation.z = Math.max(-Math.PI/4, Math.min(Math.PI/4, this.airplaneState.rotation.z));

        // 计算前进方向
        const forward = new THREE.Vector3(
            Math.sin(this.airplaneState.rotation.y),
            Math.sin(this.airplaneState.rotation.x),
            Math.cos(this.airplaneState.rotation.y)
        );

        // 应用推力
        this.airplaneState.velocity.x += forward.x * thrust * deltaTime;
        this.airplaneState.velocity.y += forward.y * thrust * deltaTime;
        this.airplaneState.velocity.z += forward.z * thrust * deltaTime;

        // 重力
        this.airplaneState.velocity.y -= 20 * deltaTime;

        // 空气阻力
        const drag = 0.98;
        this.airplaneState.velocity.x *= drag;
        this.airplaneState.velocity.y *= drag;
        this.airplaneState.velocity.z *= drag;

        // 更新位置
        this.airplaneState.position.x += this.airplaneState.velocity.x * deltaTime;
        this.airplaneState.position.y += this.airplaneState.velocity.y * deltaTime;
        this.airplaneState.position.z += this.airplaneState.velocity.z * deltaTime;

        // 防止飞机撞地
        if (this.airplaneState.position.y < 5) {
            this.airplaneState.position.y = 5;
            this.airplaneState.velocity.y = Math.max(0, this.airplaneState.velocity.y);
        }

        // 计算速度和高度
        this.airplaneState.speed = Math.sqrt(
            this.airplaneState.velocity.x ** 2 +
            this.airplaneState.velocity.z ** 2
        ) * 3.6; // 转换为km/h

        this.airplaneState.altitude = this.airplaneState.position.y;
        this.airplaneState.heading = (this.airplaneState.rotation.y * 180 / Math.PI + 360) % 360;
    }

    updateAirplane() {
        // 更新飞机位置和旋转
        this.airplane.position.set(
            this.airplaneState.position.x,
            this.airplaneState.position.y,
            this.airplaneState.position.z
        );

        this.airplane.rotation.set(
            this.airplaneState.rotation.x,
            this.airplaneState.rotation.y,
            this.airplaneState.rotation.z
        );
    }

    updateCamera() {
        // 第三人称视角跟随飞机
        const offset = new THREE.Vector3(0, 5, -20);
        offset.applyQuaternion(this.airplane.quaternion);

        this.camera.position.copy(this.airplane.position).add(offset);
        this.camera.lookAt(this.airplane.position);
    }

    updateUI() {
        // 更新仪表盘
        document.getElementById('speedValue').textContent = Math.round(this.airplaneState.speed);
        document.getElementById('altitudeValue').textContent = Math.round(this.airplaneState.altitude);
        document.getElementById('headingValue').textContent = Math.round(this.airplaneState.heading);
    }

    addCrosshair() {
        // 添加十字准星到HTML
        const crosshair = document.createElement('div');
        crosshair.className = 'crosshair';
        document.body.appendChild(crosshair);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        // 更新物理
        this.updatePhysics();

        // 更新飞机
        this.updateAirplane();

        // 更新相机
        this.updateCamera();

        // 更新UI
        this.updateUI();

        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }
}

// 当页面加载完成后启动游戏
window.addEventListener('load', () => {
    new FlightSimulator();
});
