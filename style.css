* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #000;
    overflow: hidden;
    color: white;
}

#gameContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1;
}

#gameUI {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.instrument {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #00ff00;
    border-radius: 10px;
    padding: 15px;
    min-width: 120px;
    text-align: center;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
}

.instrument h3 {
    font-size: 14px;
    margin-bottom: 8px;
    color: #00ff00;
    text-transform: uppercase;
}

.instrument div {
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 5px;
}

.instrument span {
    font-size: 12px;
    color: #cccccc;
}

#controls {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 100;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #0088ff;
    border-radius: 10px;
    padding: 20px;
    max-width: 300px;
}

#gameInfo {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 100;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #ff8800;
    border-radius: 10px;
    padding: 15px;
    min-width: 200px;
}

.info-panel h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #0088ff;
    text-transform: uppercase;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.key {
    background: #333;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

.description {
    color: #cccccc;
    margin-left: 10px;
}

#gameStatus {
    font-size: 16px;
    color: #ffffff;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #controls, #gameInfo {
        font-size: 12px;
        padding: 10px;
    }
    
    .instrument {
        padding: 10px;
        min-width: 100px;
    }
    
    .instrument div {
        font-size: 20px;
    }
}

/* 加载动画 */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 200;
    text-align: center;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #333;
    border-top: 4px solid #00ff00;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 十字准星 */
.crosshair {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    width: 30px;
    height: 30px;
    pointer-events: none;
}

.crosshair::before,
.crosshair::after {
    content: '';
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
}

.crosshair::before {
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    transform: translateY(-50%);
}

.crosshair::after {
    left: 50%;
    top: 0;
    width: 2px;
    height: 100%;
    transform: translateX(-50%);
}
