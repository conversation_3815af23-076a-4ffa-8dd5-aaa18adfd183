* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
    color: white;
    cursor: none;
}

#gameContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1;
}

/* 飞机仪表盘容器 */
#gameUI {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 100;
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: flex-end;
}

/* 圆形仪表盘基础样式 */
.instrument {
    position: relative;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, #1a1a1a 0%, #0d0d0d 70%, #000000 100%);
    border: 3px solid #333;
    border-radius: 50%;
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.8),
        0 0 30px rgba(0, 150, 255, 0.3),
        0 0 60px rgba(0, 150, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.instrument::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border: 1px solid #444;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.instrument h3 {
    font-family: 'Orbitron', monospace;
    font-size: 10px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #00aaff;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(0, 170, 255, 0.8);
    z-index: 2;
}

.instrument-value {
    font-family: 'Orbitron', monospace;
    font-size: 18px;
    font-weight: 900;
    color: #ffffff;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
    z-index: 2;
    line-height: 1;
}

.instrument-unit {
    font-family: 'Orbitron', monospace;
    font-size: 8px;
    color: #aaaaaa;
    margin-top: 2px;
    z-index: 2;
}

/* 速度表特殊样式 */
#speedometer {
    border-color: #ff6b35;
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.8),
        0 0 30px rgba(255, 107, 53, 0.4),
        0 0 60px rgba(255, 107, 53, 0.2);
}

#speedometer h3 {
    color: #ff6b35;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.8);
}

/* 高度表特殊样式 */
#altimeter {
    border-color: #4ecdc4;
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.8),
        0 0 30px rgba(78, 205, 196, 0.4),
        0 0 60px rgba(78, 205, 196, 0.2);
}

#altimeter h3 {
    color: #4ecdc4;
    text-shadow: 0 0 10px rgba(78, 205, 196, 0.8);
}

/* 方向表特殊样式 */
#compass {
    border-color: #45b7d1;
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.8),
        0 0 30px rgba(69, 183, 209, 0.4),
        0 0 60px rgba(69, 183, 209, 0.2);
}

#compass h3 {
    color: #45b7d1;
    text-shadow: 0 0 10px rgba(69, 183, 209, 0.8);
}

/* 人工地平仪 */
#attitude-indicator {
    width: 140px;
    height: 140px;
    border-color: #f39c12;
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.8),
        0 0 30px rgba(243, 156, 18, 0.4),
        0 0 60px rgba(243, 156, 18, 0.2);
}

#attitude-indicator h3 {
    color: #f39c12;
    text-shadow: 0 0 10px rgba(243, 156, 18, 0.8);
}

.attitude-display {
    position: absolute;
    width: 80px;
    height: 80px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #444;
}

.attitude-sky {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to bottom, #87CEEB, #4682B4);
}

.attitude-ground {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, #8B4513, #A0522D);
}

.attitude-horizon {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background: #ffffff;
    transform: translateY(-50%);
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.attitude-aircraft {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 2px;
    background: #ffff00;
    z-index: 10;
}

.attitude-aircraft::before,
.attitude-aircraft::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 8px;
    height: 2px;
    background: #ffff00;
    transform: translateY(-50%);
}

.attitude-aircraft::before {
    left: -12px;
}

.attitude-aircraft::after {
    right: -12px;
}

/* 控制面板 */
#controls {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 100;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 40, 0.9) 100%);
    border: 2px solid #0088ff;
    border-radius: 15px;
    padding: 20px;
    max-width: 320px;
    backdrop-filter: blur(10px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 游戏信息面板 */
#gameInfo {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(40, 20, 20, 0.9) 100%);
    border: 2px solid #ff8800;
    border-radius: 15px;
    padding: 15px;
    min-width: 200px;
    backdrop-filter: blur(10px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.info-panel h3 {
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #0088ff;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 10px rgba(0, 136, 255, 0.6);
    border-bottom: 1px solid rgba(0, 136, 255, 0.3);
    padding-bottom: 8px;
}

#gameInfo h3 {
    color: #ff8800;
    text-shadow: 0 0 10px rgba(255, 136, 0, 0.6);
    border-bottom-color: rgba(255, 136, 0, 0.3);
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.control-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.key {
    background: linear-gradient(135deg, #333 0%, #555 100%);
    color: #fff;
    padding: 6px 12px;
    border-radius: 8px;
    font-family: 'Orbitron', monospace;
    font-weight: bold;
    min-width: 70px;
    text-align: center;
    border: 1px solid #666;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.description {
    color: #e0e0e0;
    margin-left: 15px;
    font-weight: 500;
}

#gameStatus {
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    color: #ffffff;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.6);
}

/* HUD抬头显示 */
.hud-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 50;
}

/* 驾驶舱HUD */
.cockpit-hud {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-family: 'Orbitron', monospace;
    color: #00ff88;
    text-shadow: 0 0 10px #00ff88;
}

.hud-info {
    position: absolute;
    padding: 20px;
}

.hud-info.top-left {
    top: 0;
    left: 0;
}

.hud-info.top-right {
    top: 0;
    right: 0;
}

.hud-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    min-width: 120px;
    background: rgba(0, 0, 0, 0.3);
    padding: 5px 10px;
    border: 1px solid rgba(0, 255, 136, 0.5);
    border-radius: 3px;
}

.hud-label {
    font-size: 12px;
    font-weight: bold;
    color: #00ff88;
}

.hud-value {
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    text-align: right;
}

/* HUD人工地平线 */
.hud-horizon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 100px;
    border: 2px solid rgba(0, 255, 136, 0.6);
    border-radius: 10px;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.2);
}

.horizon-line {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background: #00ff88;
    transform: translateY(-50%);
    box-shadow: 0 0 10px #00ff88;
    transition: transform 0.1s ease;
}

.aircraft-symbol {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    color: #ffff00;
    text-shadow: 0 0 10px #ffff00;
    z-index: 10;
}

/* 十字准星 */
.crosshair {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 60;
    width: 40px;
    height: 40px;
    pointer-events: none;
}

.crosshair::before,
.crosshair::after {
    content: '';
    position: absolute;
    background: rgba(0, 255, 100, 0.9);
    box-shadow: 0 0 10px rgba(0, 255, 100, 0.6);
}

.crosshair::before {
    top: 50%;
    left: 15%;
    width: 70%;
    height: 2px;
    transform: translateY(-50%);
}

.crosshair::after {
    left: 50%;
    top: 15%;
    width: 2px;
    height: 70%;
    transform: translateX(-50%);
}

/* 中心圆点 */
.crosshair .center-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: rgba(0, 255, 100, 0.9);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 8px rgba(0, 255, 100, 0.8);
}

/* 速度矢量指示器 */
.velocity-vector {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 0, 0.8);
    border-radius: 50%;
    pointer-events: none;
    z-index: 55;
}

.velocity-vector::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 0, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    #controls, #gameInfo {
        font-size: 12px;
        padding: 15px;
        max-width: 280px;
    }

    .instrument {
        width: 100px;
        height: 100px;
    }

    #attitude-indicator {
        width: 120px;
        height: 120px;
    }

    .instrument-value {
        font-size: 16px;
    }

    #gameUI {
        gap: 15px;
    }
}

/* 加载动画 */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 200;
    text-align: center;
    font-family: 'Orbitron', monospace;
    color: #00aaff;
}

.loading::after {
    content: '';
    display: block;
    width: 60px;
    height: 60px;
    margin: 20px auto;
    border: 4px solid rgba(0, 170, 255, 0.3);
    border-top: 4px solid #00aaff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    box-shadow: 0 0 20px rgba(0, 170, 255, 0.5);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 脉冲动画 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.pulse {
    animation: pulse 2s ease-in-out infinite;
}

/* 发光效果 */
.glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
    }
    to {
        text-shadow: 0 0 20px currentColor, 0 0 30px currentColor, 0 0 40px currentColor;
    }
}
