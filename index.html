<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D飞机模拟游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏画布将在这里渲染 -->
    </div>
    
    <!-- 游戏UI -->
    <div id="gameUI">
        <div id="speedometer" class="instrument">
            <h3>速度</h3>
            <div id="speedValue">0</div>
            <span>km/h</span>
        </div>
        
        <div id="altimeter" class="instrument">
            <h3>高度</h3>
            <div id="altitudeValue">0</div>
            <span>米</span>
        </div>
        
        <div id="compass" class="instrument">
            <h3>方向</h3>
            <div id="headingValue">0</div>
            <span>度</span>
        </div>
    </div>
    
    <!-- 控制说明 -->
    <div id="controls" class="info-panel">
        <h3>控制说明</h3>
        <div class="control-item">
            <span class="key">W/S</span>
            <span class="description">俯仰控制（上升/下降）</span>
        </div>
        <div class="control-item">
            <span class="key">A/D</span>
            <span class="description">横滚控制（左右倾斜）</span>
        </div>
        <div class="control-item">
            <span class="key">Q/E</span>
            <span class="description">偏航控制（左右转向）</span>
        </div>
        <div class="control-item">
            <span class="key">鼠标</span>
            <span class="description">视角控制</span>
        </div>
        <div class="control-item">
            <span class="key">空格</span>
            <span class="description">加速</span>
        </div>
    </div>
    
    <!-- 游戏信息 -->
    <div id="gameInfo" class="info-panel">
        <h3>游戏状态</h3>
        <div id="gameStatus">准备就绪</div>
    </div>
    
    <!-- 加载Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="game.js"></script>
</body>
</html>
