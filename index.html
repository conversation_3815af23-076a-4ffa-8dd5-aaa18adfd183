<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D飞机模拟游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏画布将在这里渲染 -->
    </div>
    
    <!-- HUD抬头显示 -->
    <div class="hud-overlay">
        <div class="crosshair">
            <div class="center-dot"></div>
        </div>
        <div class="velocity-vector" id="velocityVector"></div>

        <!-- 驾驶舱HUD信息 -->
        <div class="cockpit-hud">
            <div class="hud-info top-left">
                <div class="hud-item">
                    <span class="hud-label">SPD</span>
                    <span class="hud-value" id="hudSpeed">0</span>
                </div>
                <div class="hud-item">
                    <span class="hud-label">ALT</span>
                    <span class="hud-value" id="hudAltitude">0</span>
                </div>
            </div>

            <div class="hud-info top-right">
                <div class="hud-item">
                    <span class="hud-label">HDG</span>
                    <span class="hud-value" id="hudHeading">0</span>
                </div>
                <div class="hud-item">
                    <span class="hud-label">G</span>
                    <span class="hud-value" id="hudGForce">1.0</span>
                </div>
            </div>

            <!-- 人工地平线HUD -->
            <div class="hud-horizon">
                <div class="horizon-line" id="hudHorizonLine"></div>
                <div class="aircraft-symbol">✈</div>
            </div>
        </div>
    </div>

    <!-- 飞机仪表盘 -->
    <div id="gameUI">
        <!-- 人工地平仪 -->
        <div id="attitude-indicator" class="instrument">
            <h3>姿态</h3>
            <div class="attitude-display">
                <div class="attitude-sky"></div>
                <div class="attitude-ground"></div>
                <div class="attitude-horizon" id="attitudeHorizon"></div>
                <div class="attitude-aircraft"></div>
            </div>
        </div>

        <!-- 速度表 -->
        <div id="speedometer" class="instrument">
            <h3>空速</h3>
            <div class="instrument-value" id="speedValue">0</div>
            <div class="instrument-unit">节</div>
        </div>

        <!-- 高度表 -->
        <div id="altimeter" class="instrument">
            <h3>高度</h3>
            <div class="instrument-value" id="altitudeValue">0</div>
            <div class="instrument-unit">英尺</div>
        </div>

        <!-- 方向表 -->
        <div id="compass" class="instrument">
            <h3>航向</h3>
            <div class="instrument-value" id="headingValue">0</div>
            <div class="instrument-unit">度</div>
        </div>
    </div>
    
    <!-- 控制说明 -->
    <div id="controls" class="info-panel">
        <h3>控制说明</h3>
        <div class="control-item">
            <span class="key">W/S</span>
            <span class="description">俯仰控制（上升/下降）</span>
        </div>
        <div class="control-item">
            <span class="key">A/D</span>
            <span class="description">横滚控制（左右倾斜）</span>
        </div>
        <div class="control-item">
            <span class="key">Q/E</span>
            <span class="description">偏航控制（左右转向）</span>
        </div>
        <div class="control-item">
            <span class="key">鼠标</span>
            <span class="description">视角控制</span>
        </div>
        <div class="control-item">
            <span class="key">空格</span>
            <span class="description">加速</span>
        </div>
    </div>
    
    <!-- 游戏信息 -->
    <div id="gameInfo" class="info-panel">
        <h3>游戏状态</h3>
        <div id="gameStatus">准备就绪</div>
    </div>
    
    <!-- 加载Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="game.js"></script>
</body>
</html>
