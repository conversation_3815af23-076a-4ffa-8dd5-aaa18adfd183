# 3D飞机模拟游戏

一个基于Three.js的网页3D飞机模拟游戏，可以在浏览器中直接游玩。

## 功能特性

- 🛩️ 3D飞机模型和飞行物理
- 🌤️ 3D环境（地形、云朵、天空）
- 🎮 键盘和鼠标控制
- 📊 实时仪表盘显示
- 🎯 第三人称视角跟随
- 💨 物理效果（重力、空气阻力、惯性）

## 如何游玩

### 启动游戏
1. 确保有网络连接（需要加载Three.js库）
2. 用浏览器打开 `index.html` 文件
3. 点击页面锁定鼠标指针（可选）

### 控制说明

#### 键盘控制
- **W/S**: 俯仰控制（机头上升/下降）
- **A/D**: 横滚控制（向左/右倾斜）
- **Q/E**: 偏航控制（向左/右转向）
- **空格**: 加速推进

#### 鼠标控制
- **鼠标移动**: 控制飞机方向
- **点击**: 锁定鼠标指针

### 仪表盘说明
- **速度表**: 显示当前飞行速度（km/h）
- **高度表**: 显示当前飞行高度（米）
- **方向表**: 显示当前飞行方向（度）

## 游戏特色

### 飞行物理
- 真实的重力效果
- 空气阻力模拟
- 飞机惯性系统
- 防撞地保护

### 3D环境
- 随机生成的地形
- 漂浮的云朵
- 动态光照和阴影
- 雾化效果增强距离感

### 视觉效果
- 平滑的相机跟随
- 实时阴影渲染
- 十字准星瞄准
- 响应式UI设计

## 技术实现

- **Three.js**: 3D图形渲染
- **HTML5**: 游戏界面
- **CSS3**: 样式和动画
- **JavaScript**: 游戏逻辑和物理

## 系统要求

- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 支持WebGL的显卡
- 网络连接（首次加载）

## 开发说明

### 文件结构
```
├── index.html      # 主HTML文件
├── style.css       # 样式文件
├── game.js         # 游戏逻辑
└── README.md       # 说明文档
```

### 自定义修改
- 修改 `game.js` 中的飞机模型
- 调整物理参数（重力、阻力等）
- 添加更多地形特征
- 增加游戏目标和任务

## 未来改进

- [ ] 添加更多飞机模型
- [ ] 增加天气系统
- [ ] 添加任务和目标
- [ ] 多人游戏支持
- [ ] 音效和背景音乐
- [ ] 更复杂的地形生成
- [ ] 飞机损伤系统

## 许可证

本项目仅供学习和娱乐使用。

---

享受你的飞行体验！🛩️✈️
